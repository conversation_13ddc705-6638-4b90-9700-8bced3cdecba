
# 1. Error and exception handling
# Question: 
# 	- Create a function that parses a list of strings into integers and returns their sum. Handle multiple possible exceptions that may arise.
# Requirements:
# 	- Accept a list of strings and convert them to integers.
# 	- If a string cannot be converted, log the error and continue processing the rest.
# 	- If an empty list is provided, raise a custom exception called EmptyListError.
# 	- If the sum exceeds 1000, raise a ValueError with a message: "Sum exceeds the allowed limit."

class EmptyListError(Exception):
    pass

def parse_and_sum(strings):
    if not strings:
        raise EmptyListError("The list is empty.")

    total = 0
    for string in strings:
        try:
            number = int(string)
            total += number
        except ValueError:
            print(f"Error: '{string}' is not a valid integer.")

    if total > 1000:
        raise ValueError("Sum exceeds the allowed limit.")

    return total


    

    
        
        