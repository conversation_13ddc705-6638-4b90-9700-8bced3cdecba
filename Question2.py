
# 2. Loggers and custom loggers
# Question:
# 	- Write a custom logger that tracks the execution time of a function and logs exceptions if they occur.
# Requirements:
# 	The logger should:
# 	-Log INFO when a function starts.
# 	-Log WARNING if execution time exceeds 2 seconds.
# 	-Log ERROR if an exception occurs.

# 	Implement a decorator @log_execution that wraps around any function and logs:
# 	-Function name
# 	-Execution time
# 	-Any raised exceptions
\




