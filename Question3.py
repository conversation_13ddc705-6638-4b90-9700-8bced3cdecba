# 3. Threading and parallel processing
# Question:
# 	- Write a Python program that creates and starts three threads, where each thread prints "Hello from Thread X" (X being the thread number).
# Requirements:
# 	- Use the threading module.
# 	- Create three threads, each running a function that prints its thread number.
# 	- Ensure the main program waits for all threads to finish before exiting.

from threading import Thread
import time
class cls1:
    def greet(Thread):
        print(Thread)
        time.sleep(1)
class cls2:
     def wel(Thread):
        print(Thread)
        time.sleep(1)
class cls3:
     def Good(Thread):
        print(Thread)
        time.sleep(1)

t1=cls1()
t2=cls2()
t3=cls3()


    
